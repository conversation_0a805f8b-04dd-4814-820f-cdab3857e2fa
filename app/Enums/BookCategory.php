<?php

namespace App\Enums;

enum BookCategory: string
{
    case FICTION = 'fiction';
    case NON_FICTION = 'non_fiction';
    case SCIENCE = 'science';
    case TECHNOLOGY = 'technology';
    case HISTORY = 'history';
    case BIOGRAPHY = 'biography';
    case MYSTERY = 'mystery';
    case ROMANCE = 'romance';
    case FANTASY = 'fantasy';
    case HORROR = 'horror';
    case THRILLER = 'thriller';
    case CHILDREN = 'children';
    case YOUNG_ADULT = 'young_adult';
    case SELF_HELP = 'self_help';
    case HEALTH = 'health';
    case COOKING = 'cooking';
    case TRAVEL = 'travel';
    case RELIGION = 'religion';
    case PHILOSOPHY = 'philosophy';
    case POETRY = 'poetry';
    case DRAMA = 'drama';
    case COMEDY = 'comedy';
    case EDUCATION = 'education';
    case BUSINESS = 'business';
    case ECONOMICS = 'economics';
    case POLITICS = 'politics';
    case PSYCHOLOGY = 'psychology';
    case SOCIOLOGY = 'sociology';
    case ART = 'art';
    case MUSIC = 'music';

    /**
     * Get a random category value
     */
    public static function random(): self
    {
        $cases = self::cases();

        return $cases[array_rand($cases)];
    }

    /**
     * Get all category values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
