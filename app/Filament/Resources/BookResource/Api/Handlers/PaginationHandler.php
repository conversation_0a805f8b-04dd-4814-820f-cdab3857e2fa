<?php

namespace App\Filament\Resources\BookResource\Api\Handlers;

use App\Filament\Resources\BookResource;
use App\Filament\Resources\BookResource\Api\Transformers\BookTransformer;
use App\Models\Book;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Rupadana\ApiService\Http\Handlers;
use Spatie\QueryBuilder\QueryBuilder;

#[Group('Books')]
class PaginationHandler extends Handlers
{
    public static ?string $uri = '/';
    public static ?string $resource = BookResource::class;

    /**
     * List of Book
     */
    public function handler(): AnonymousResourceCollection
    {
        /** @var Builder<Book> $query */
        $query = static::getEloquentQuery();

        $query = QueryBuilder::for($query)
            ->allowedFields($this->getAllowedFields())
            ->allowedSorts($this->getAllowedSorts())
            ->allowedFilters($this->getAllowedFilters())
            ->allowedIncludes($this->getAllowedIncludes())
            ->paginate((int) request()->query('per_page'))
            ->appends(request()->query());

        return BookTransformer::collection($query);
    }
}
